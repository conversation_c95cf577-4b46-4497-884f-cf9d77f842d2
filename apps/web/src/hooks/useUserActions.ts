import { createOptimisticListActions } from "@/utils/createOptimisticActions";
import { orpc } from "@/utils/orpc";
import type { UserModelType } from "../../../server/prisma/generated/zod/schemas";

export const useUserActions = (
	params: { page?: number; limit?: number } = {},
) =>
	createOptimisticListActions<UserModelType>(
		[
			...orpc.users.paginate.queryOptions({
				input: { page: params.page ?? 1, limit: params.limit ?? 10 },
			}).queryKey,
		],
		{
			update: async (variables) =>
				orpc.users.update.mutationOptions().mutateAsync({ input: variables }),
			delete: async (id) => orpc.users.delete.mutationOptions().mutateAsync({ input: { id } }),
			create: async (variables) =>
				orpc.users.create.mutationOptions().mutateAsync({ input: variables }),
		},
	);

